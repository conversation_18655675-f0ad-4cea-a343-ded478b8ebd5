
import { useState, useEffect } from 'react'

function App() {
  const [url, setUrl] = useState('')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)

  // Sample AI-generated images with better quality and variety
  const aiImages = [
    {
      url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop&crop=center',
      title: 'AI Digital Art',
      description: 'Futuristic digital artwork'
    },
    {
      url: 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=800&h=600&fit=crop&crop=center',
      title: 'AI Portrait',
      description: 'AI-generated human portrait'
    },
    {
      url: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=800&h=600&fit=crop&crop=center',
      title: 'AI Landscape',
      description: 'Surreal AI landscape'
    },
    {
      url: 'https://images.unsplash.com/photo-1677756119517-756a188d2d94?w=800&h=600&fit=crop&crop=center',
      title: 'AI Abstract',
      description: 'Abstract AI creation'
    },
    {
      url: 'https://images.unsplash.com/photo-1686191128892-3b4e0b5b2e8e?w=800&h=600&fit=crop&crop=center',
      title: 'AI Technology',
      description: 'Tech-inspired AI art'
    }
  ]

  // Auto-rotate images every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === aiImages.length - 1 ? 0 : prevIndex + 1
      )
    }, 4000)

    return () => clearInterval(interval)
  }, [aiImages.length])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (url) {
      setIsGenerating(true)
      console.log('Generating video for URL:', url)

      // Simulate processing time
      setTimeout(() => {
        setIsGenerating(false)
        alert(`✅ AI video generated successfully for: ${url}`)
      }, 3000)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-500"></div>
      </div>

      {/* Navigation Header */}
      <nav className="relative z-10 px-6 py-6 border-b border-purple-500/20 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                AI VideoGen
              </h2>
              <p className="text-xs text-gray-400">Powered by Advanced AI</p>
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            <button className="text-gray-300 hover:text-white transition-colors px-4 py-2 rounded-lg hover:bg-purple-500/20">
              Features
            </button>
            <button className="text-gray-300 hover:text-white transition-colors px-4 py-2 rounded-lg hover:bg-purple-500/20">
              Pricing
            </button>
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
              Get Started
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-12">
        <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">

          {/* Left Side - Enhanced Search Menu */}
          <div className="space-y-10">
            {/* Hero Text */}
            <div className="text-center lg:text-left space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 rounded-full border border-purple-500/30 mb-6">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                <span className="text-sm text-purple-200 font-medium">AI-Powered Video Generation</span>
              </div>

              <h1 className="text-5xl lg:text-7xl font-extrabold leading-tight">
                <span className="bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent">
                  Transform Videos
                </span>
                <br />
                <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent">
                  with AI Magic
                </span>
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
                Experience the future of video creation. Our advanced AI transforms any video URL into
                stunning, professional content in minutes. No technical skills required.
              </p>
            </div>

            {/* Enhanced Search Form */}
            <div className="bg-gradient-to-br from-white/10 to-purple-500/10 rounded-3xl p-8 backdrop-blur-lg border border-white/20 shadow-2xl">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Start Creating Now</h3>
                <p className="text-gray-300">Paste your video URL and watch the magic happen</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="relative">
                  <label htmlFor="url" className="block text-sm font-semibold text-purple-200 mb-3">
                    🎬 Video URL
                  </label>
                  <div className="relative">
                    <input
                      type="url"
                      id="url"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=example"
                      className="w-full px-6 py-4 bg-gray-900/50 border-2 border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 text-lg"
                      required
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-purple-500 via-pink-500 to-purple-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-purple-600 hover:via-pink-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-purple-500/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isGenerating ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Generating AI Video...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span>Generate AI Video</span>
                    </div>
                  )}
                </button>
              </form>

              {/* Enhanced Features List */}
              <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                  <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-green-300">Multi-Platform</p>
                    <p className="text-xs text-green-400/80">YouTube, Vimeo, TikTok</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                  <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-blue-300">Lightning Fast</p>
                    <p className="text-xs text-blue-400/80">2-5 minutes processing</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-purple-500/10 rounded-lg border border-purple-500/20">
                  <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-purple-300">4K Quality</p>
                    <p className="text-xs text-purple-400/80">Ultra HD output</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-pink-500/10 rounded-lg border border-pink-500/20">
                  <div className="w-8 h-8 bg-pink-500/20 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-pink-300">Secure</p>
                    <p className="text-xs text-pink-400/80">Privacy protected</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Enhanced AI Gallery */}
          <div className="relative">
            {/* Main Display Card */}
            <div className="relative z-10 bg-gradient-to-br from-white/10 to-purple-500/10 rounded-3xl p-6 backdrop-blur-lg border border-white/20 shadow-2xl">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-xl font-bold text-white">AI Gallery</h3>
                  <p className="text-sm text-gray-300">Live AI-generated content</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400 font-medium">LIVE</span>
                </div>
              </div>

              {/* Main Image Display */}
              <div className="relative aspect-video w-full rounded-2xl overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800 shadow-xl">
                <img
                  src={aiImages[currentImageIndex].url}
                  alt={aiImages[currentImageIndex].title}
                  className="w-full h-full object-cover transition-all duration-1000 ease-in-out hover:scale-105"
                />

                {/* Image Overlay Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                  <h4 className="text-white font-bold text-lg mb-1">
                    {aiImages[currentImageIndex].title}
                  </h4>
                  <p className="text-gray-300 text-sm">
                    {aiImages[currentImageIndex].description}
                  </p>
                </div>

                {/* Loading Animation Overlay */}
                <div className="absolute top-4 right-4 bg-black/50 rounded-full p-2 backdrop-blur-sm">
                  <svg className="w-4 h-4 text-purple-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </div>
              </div>

              {/* Enhanced Image Indicators */}
              <div className="flex justify-center mt-6 space-x-3">
                {aiImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`group relative transition-all duration-300 ${
                      index === currentImageIndex
                        ? 'scale-110'
                        : 'hover:scale-105'
                    }`}
                  >
                    <div className={`w-12 h-8 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                      index === currentImageIndex
                        ? 'border-purple-400 shadow-lg shadow-purple-400/50'
                        : 'border-gray-600 hover:border-gray-400'
                    }`}>
                      <img
                        src={image.url}
                        alt={image.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {index === currentImageIndex && (
                      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-purple-400 rounded-full"></div>
                    )}
                  </button>
                ))}
              </div>

              {/* Stats Section */}
              <div className="mt-8 grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-gradient-to-br from-purple-500/20 to-transparent rounded-xl border border-purple-500/30">
                  <div className="text-2xl font-bold text-white mb-1">1M+</div>
                  <div className="text-xs text-gray-400">Videos Generated</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-pink-500/20 to-transparent rounded-xl border border-pink-500/30">
                  <div className="text-2xl font-bold text-white mb-1">4K</div>
                  <div className="text-xs text-gray-400">Ultra HD Quality</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-blue-500/20 to-transparent rounded-xl border border-blue-500/30">
                  <div className="text-2xl font-bold text-white mb-1">99%</div>
                  <div className="text-xs text-gray-400">Success Rate</div>
                </div>
              </div>

              {/* Floating Animation Elements */}
              <div className="absolute top-8 right-8 w-3 h-3 bg-purple-400 rounded-full animate-bounce"></div>
              <div className="absolute bottom-8 left-8 w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-6 w-1 h-1 bg-blue-400 rounded-full animate-ping delay-500"></div>
              <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full animate-pulse delay-700"></div>
            </div>

            {/* Enhanced Background Effects */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 rounded-3xl blur-2xl transform rotate-1"></div>
            <div className="absolute inset-0 bg-gradient-to-l from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-xl transform -rotate-1"></div>
          </div>
        </div>

        {/* Bottom Section - Additional Features */}
        <div className="mt-20 text-center">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full border border-purple-500/30 backdrop-blur-sm">
            <svg className="w-5 h-5 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-purple-200 font-medium">Powered by Advanced Neural Networks</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
