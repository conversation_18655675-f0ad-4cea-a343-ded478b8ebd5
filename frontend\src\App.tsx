
import { useState, useEffect } from 'react'

function App() {
  const [url, setUrl] = useState('')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Sample AI-generated images (you can replace these with actual AI-generated images)
  const aiImages = [
    'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=600&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=600&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1677756119517-756a188d2d94?w=600&h=400&fit=crop&crop=center',
    'https://images.unsplash.com/photo-1686191128892-3b4e0b5b2e8e?w=600&h=400&fit=crop&crop=center'
  ]

  // Auto-rotate images every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === aiImages.length - 1 ? 0 : prevIndex + 1
      )
    }, 3000)

    return () => clearInterval(interval)
  }, [aiImages.length])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (url) {
      console.log('Generating video for URL:', url)
      // Add your video generation logic here
      alert(`Generating AI video for: ${url}`)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-6 py-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-screen">

          {/* Left Side - URL Input Form */}
          <div className="space-y-8">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl lg:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent mb-6">
                AI Video Generator
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-lg">
                Transform any video URL into stunning AI-generated content.
                Simply paste your link and let our AI work its magic.
              </p>
            </div>

            {/* URL Input Form */}
            <div className="bg-gradient-to-br from-purple-900/50 to-pink-900/50 rounded-2xl p-8 backdrop-blur-sm border border-purple-500/30">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-gray-300 mb-2">
                    Video URL
                  </label>
                  <input
                    type="url"
                    id="url"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=..."
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25"
                >
                  Generate AI Video
                </button>
              </form>

              {/* Additional Info */}
              <div className="mt-6 text-sm text-gray-400">
                <p>✨ Supports YouTube, Vimeo, and direct video links</p>
                <p>🚀 Processing time: 2-5 minutes</p>
                <p>🎯 High-quality AI enhancement</p>
              </div>
            </div>
          </div>

          {/* Right Side - AI Generated Images Display */}
          <div className="relative">
            <div className="relative z-10 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-3xl p-8 backdrop-blur-sm border border-purple-500/30">
              <div className="aspect-video w-full rounded-2xl overflow-hidden bg-gray-800/50">
                <img
                  src={aiImages[currentImageIndex]}
                  alt={`AI Generated Content ${currentImageIndex + 1}`}
                  className="w-full h-full object-cover transition-all duration-1000 ease-in-out"
                />
              </div>

              {/* Image Indicators */}
              <div className="flex justify-center mt-6 space-x-2">
                {aiImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentImageIndex
                        ? 'bg-purple-400 scale-125'
                        : 'bg-gray-600 hover:bg-gray-500'
                    }`}
                  />
                ))}
              </div>

              {/* Floating Elements */}
              <div className="absolute top-4 right-4 w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
              <div className="absolute bottom-4 left-4 w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-4 w-1 h-1 bg-white rounded-full animate-pulse delay-500"></div>
            </div>

            {/* Background Glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-3xl blur-xl"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
