import React, { useState } from "react";

function App() {
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleConvert = () => {
    const replaced = input.split(" ").join("@40");
    setOutput(replaced);
  };

  return (
    <div style={styles.container}>
      <h1 style={styles.heading}>Space Replacer 🚀</h1>
      <input
        type="text"
        value={input}
        onChange={handleChange}
        placeholder="Type something..."
        style={styles.input}
      />
      <button onClick={handleConvert} style={styles.button}>
        Convert Spaces
      </button>
      {output && (
        <div style={styles.output}>
          <strong>Result:</strong> {output}
        </div>
      )}
    </div>
  );
}

const styles: { [key: string]: React.CSSProperties } = {
  container: {
    fontFamily: "sans-serif",
    maxWidth: "400px",
    margin: "50px auto",
    padding: "30px",
    borderRadius: "12px",
    boxShadow: "0 4px 10px rgba(0,0,0,0.1)",
    backgroundColor: "#fefefe",
  },
  heading: {
    textAlign: "center",
    color: "#333",
  },
  input: {
    width: "100%",
    padding: "10px",
    fontSize: "16px",
    marginBottom: "15px",
    border: "1px solid #ccc",
    borderRadius: "8px",
  },
  button: {
    width: "100%",
    padding: "10px",
    fontSize: "16px",
    backgroundColor: "#0070f3",
    color: "white",
    border: "none",
    borderRadius: "8px",
    cursor: "pointer",
  },
  output: {
    marginTop: "20px",
    fontSize: "18px",
    color: "#444",
  },
};

export default App;
